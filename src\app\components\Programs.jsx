import React from 'react';
import { Clock, Users, Lightbulb, Heart, Gamepad2, Calculator } from 'lucide-react';

const Programs = () => {
  const programs = [
    {
      icon: Users,
      title: "Reading & Language Arts",
      description: "Building phonics, phonemic awareness, vocabulary, and communication skills through interactive storytelling and creative expression.",      age: "Ages 3-6",
      color: "blue"
    },
    {
      icon: Calculator,
      title: "Mathematics",
      description: "Developing math readiness and logical thinking through manipulatives and hands-on activities.",
      age: "Ages 3-6", 
      color: "green"
    },
    {
      icon: Lightbulb,
      title: "STEAM & Creative Arts",
      description: "Science, technology, engineering, arts, and math integration to foster innovation and creativity.",
      age: "Ages 4-6",
      color: "purple"
    },
    {
      icon: Heart,
      title: "Social Emotional Learning",
      description: "Teaching empathy, friendship skills, and emotional development through collaborative activities.",
      age: "Ages 3-6",
      color: "pink"
    },
    {
      icon: Gamepad2,
      title: "Speech & Language Support",
      description: "Specialized support for effective communication, building confidence and language skills.",
      age: "All Ages",
      color: "orange"
    },
    {
      icon: Clock,
      title: "Crisis & Collaboration",
      description: "Building resilience and conflict resolution skills with collaborative learning opportunities.",
      age: "Ages 4-6",
      color: "indigo"
    }
  ];

  const getColorClasses = (color) => {
    const colors = {
      blue: "from-blue-400 to-blue-600",
      green: "from-green-400 to-green-600", 
      purple: "from-purple-400 to-purple-600",
      pink: "from-pink-400 to-pink-600",
      orange: "from-orange-400 to-orange-600",
      indigo: "from-indigo-400 to-indigo-600"
    };
    return colors[color] || colors.blue;
  };

  return (
    <section id="programs" className="py-20 bg-white">
      <div className="max-w-6xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            Academic & Enrichment Programs
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Explore our diverse range of enriching programs from foundational academics to creative 
            arts and special development opportunities.
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-orange-500 mx-auto rounded-full mt-6"></div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {programs.map((program, index) => {
            const Icon = program.icon;
            return (
              <div 
                key={index}
                className="bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-105 overflow-hidden group"
              >
                <div className={`bg-gradient-to-r ${getColorClasses(program.color)} p-6 text-center`}>
                  <div className="bg-white/20 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Icon className="h-8 w-8 text-white" />
                  </div>
                  <span className="bg-white/20 text-white px-3 py-1 rounded-full text-sm font-medium">
                    {program.age}
                  </span>
                </div>
                
                <div className="p-6">
                  <h3 className="text-xl font-bold text-gray-800 mb-3 group-hover:text-blue-600 transition-colors">
                    {program.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {program.description}
                  </p>
                </div>
              </div>
            );
          })}
        </div>

        <div className="text-center mt-12">
          <p className="text-lg text-gray-700 mb-6">
            Ready to explore our programs in detail?
          </p>
          <button className="bg-gradient-to-r from-blue-500 to-orange-500 text-white px-8 py-3 rounded-full font-semibold hover:shadow-lg transition-all duration-300 hover:scale-105">
            Schedule a Visit
          </button>
        </div>
      </div>
    </section>
  );
};

export default Programs;