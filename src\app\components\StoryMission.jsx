import React from 'react';
import { <PERSON>, <PERSON>, Star, Target } from 'lucide-react';

const StoryMission = () => {
  return (
    <section id="about" className="py-20 bg-white">
      <div className="max-w-6xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            Our Story & Mission
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-orange-500 mx-auto rounded-full"></div>
        </div>

        <div className="grid lg:grid-cols-2 gap-12 items-center mb-16">
          <div className="space-y-6">
            <p className="text-lg text-gray-700 leading-relaxed">
              Our school began over 15 years ago with a mission to create an environment 
              where every child, regardless of their unique needs, can thrive and reach their 
              full potential in a supportive, nurturing community.
            </p>
            <p className="text-lg text-gray-700 leading-relaxed">
              Our dedicated team of educators are specialists who have created a 
              differentiated approach to learning that celebrates each child's individual 
              abilities while fostering growth through innovative methods and personal attention.
            </p>
            <p className="text-lg text-gray-700 leading-relaxed">
              We believe that early childhood is a crucial educational period where students 
              build strong foundations. Our approach ensures that learning is both 
              inclusive and accessible through structured, play-based sessions.
            </p>
          </div>
          
          <div className="relative">
            <div className="grid grid-cols-2 gap-4">
              <img 
                src="https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                alt="Children learning together"
                className="rounded-lg shadow-lg hover:scale-105 transition-transform duration-300"
              />
              <img 
                src="https://images.unsplash.com/photo-1519389950473-47ba0277781c?ixlib=rb-4.0.3&auto=format&fit=crop&w=500&q=80"
                alt="Classroom environment"
                className="rounded-lg shadow-lg hover:scale-105 transition-transform duration-300 mt-8"
              />
            </div>
            <div className="absolute -bottom-4 -right-4 bg-orange-100 rounded-full p-4">
              <Heart className="h-12 w-12 text-orange-500" />
            </div>
          </div>
        </div>

        {/* Mission Values */}
        <div className="grid md:grid-cols-3 gap-8">
          <div className="text-center p-6 bg-blue-50 rounded-xl hover:shadow-lg transition-shadow duration-300">
            <div className="bg-blue-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Users className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-bold text-gray-800 mb-3">Inclusive Community</h3>
            <p className="text-gray-600">Creating a welcoming environment where every child feels valued and supported.</p>
          </div>
          
          <div className="text-center p-6 bg-orange-50 rounded-xl hover:shadow-lg transition-shadow duration-300">
            <div className="bg-orange-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Star className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-bold text-gray-800 mb-3">Excellence in Education</h3>
            <p className="text-gray-600">Providing high-quality, individualized learning experiences for all students.</p>
          </div>
          
          <div className="text-center p-6 bg-green-50 rounded-xl hover:shadow-lg transition-shadow duration-300">
            <div className="bg-green-500 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
              <Target className="h-8 w-8 text-white" />
            </div>
            <h3 className="text-xl font-bold text-gray-800 mb-3">Purposeful Growth</h3>
            <p className="text-gray-600">Fostering development through structured, meaningful learning opportunities.</p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default StoryMission;