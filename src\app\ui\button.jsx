
"use client";
const Button = ({
  children,
  onClick,
  type = 'button',
  variant = 'primary',
  className = '',
  disabled = false,
}) => {
  const baseStyle =
    'px-4 py-2 rounded text-white font-semibold transition duration-300 cursor-pointer';
  const variants = {
    primary: 'bg-blue-600 hover:bg-blue-700 ',
    secondary: 'bg-gray-600 hover:bg-gray-700',
    danger: 'bg-red-600 hover:bg-red-700',
    success: 'bg-green-600 hover:bg-green-700',
    outline: 'bg-transparent border border-gray-600 text-gray-800 hover:bg-gray-100',
  };

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      className={`${baseStyle} ${variants[variant] || ''} ${className}`}
    >
      {children}
    </button>
  );
};

export default Button;
