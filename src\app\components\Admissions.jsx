import React from 'react';
import { FileText, Calendar, Users, CheckCircle } from 'lucide-react';
import Button from '../ui/button';

const Admissions = () => {
  const admissionSteps = [
    {
      icon: FileText,
      title: "Apply",
      description: "Submit your application form including basic information, medical records, and family details.",
      color: "blue"
    },
    {
      icon: Calendar,
      title: "Assessment",
      description: "Schedule an individual assessment to understand your child's unique needs and learning style.",
      color: "green"
    },
    {
      icon: Users,
      title: "Enrollment",
      description: "Meet with our admissions team and complete the enrollment process to secure your child's spot.",
      color: "orange"
    }
  ];

  const getColorClasses = (color) => {
    const colors = {
      blue: "bg-blue-500",
      green: "bg-green-500",
      orange: "bg-orange-500"
    };
    return colors[color] || colors.blue;
  };

  return (
    <section id="admissions" className="py-20 bg-gray-50">
      <div className="max-w-6xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            Admissions
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Join our inclusive school community! Our admissions process is designed to welcome 
            every child with care, love, and support.
          </p>          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-orange-500 mx-auto rounded-full mt-6"></div>
        </div>

        <div className="grid md:grid-cols-3 gap-8 mb-12">
          {admissionSteps.map((step, index) => {
            const Icon = step.icon;
            return (
              <div key={index} className="relative">
                <div className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 hover:scale-105">
                  <div className={`w-16 h-16 ${getColorClasses(step.color)} rounded-full flex items-center justify-center mx-auto mb-4`}>
                    <Icon className="h-8 w-8 text-white" />
                  </div>
                  <div className="text-center">
                    <h3 className="text-xl font-bold text-gray-800 mb-3">
                      {step.title}
                    </h3>
                    <p className="text-gray-600 leading-relaxed">
                      {step.description}
                    </p>
                  </div>
                </div>
                
                {/* Arrow connector (except for last item) */}
                {index < admissionSteps.length - 1 && (
                  <div className="hidden md:block absolute top-1/2 -right-4 transform -translate-y-1/2">
                    <div className="w-8 h-0.5 bg-gray-300"></div>
                    <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-0 h-0 border-l-4 border-l-gray-300 border-t-2 border-b-2 border-t-transparent border-b-transparent"></div>
                  </div>
                )}
              </div>
            );
          })}
        </div>

        {/* Application Information */}
        <div className="bg-white rounded-xl shadow-lg p-8">
          <div className="grid md:grid-cols-2 gap-8 items-center">
            <div>
              <h3 className="text-2xl font-bold text-gray-800 mb-4">
                Ready to Begin Your Journey?
              </h3>
              <p className="text-gray-600 mb-6 leading-relaxed">
                We're excited to welcome your family to our inclusive learning community. 
                Our admissions team is here to guide you through every step of the process.
              </p>
              
              <div className="space-y-3 mb-6">
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <span className="text-gray-700">Rolling admissions throughout the year</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <span className="text-gray-700">Financial aid and scholarships available</span>
                </div>
                <div className="flex items-center space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  <span className="text-gray-700">Flexible scheduling options</span>
                </div>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Button className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-full">
                  Start Application
                </Button>
                <Button variant="outline" className="border-orange-500 text-orange-600 hover:bg-orange-50 px-6 py-3 rounded-full">
                  Schedule Tour
                </Button>
              </div>
            </div>
            
            <div className="relative">
              <img 
                src="https://images.unsplash.com/photo-1519389950473-47ba0277781c?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"
                alt="Happy children learning"
                className="rounded-lg shadow-lg w-full h-80 object-cover"
              />
              <div className="absolute -bottom-4 -left-4 bg-gradient-to-r from-blue-500 to-orange-500 text-white p-4 rounded-lg shadow-lg">
                <p className="font-semibold">Applications Open!</p>
                <p className="text-sm">Join us for Fall 2024</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Admissions;