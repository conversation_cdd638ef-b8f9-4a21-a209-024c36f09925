import React from 'react';
import { Book<PERSON><PERSON>, Palette, <PERSON>, Brain, Music, Globe } from 'lucide-react';

const Curriculum = () => {
  const curriculumItems = [
    {
      icon: Brain,
      title: "Multi-Literacy Learning",
      description: "Engaging, multi-sensory approaches to reading, writing, and comprehensive literacy skills.",
      color: "blue"
    },
    {
      icon: Users,
      title: "Social-Emotional Approach",
      description: "Our highly collaborative classroom cultivates empathy, friendship, and social skills through interactive play.",
      color: "green"
    },
    {
      icon: Palette,
      title: "Personalized Goals",
      description: "We craft learning objectives specifically for each child and track their progress daily.",
      color: "orange"
    },
    {
      icon: BookOpen,
      title: "Reading & Language Arts",
      description: "Phonics, phonics awareness, vocabulary, and comprehensive literacy skills through engaging activities.",
      color: "purple"
    },
    {
      icon: Music,
      title: "Mathematics",
      description: "Hands-on math concepts that foster logical thinking through creative exploratory methods.",
      color: "pink"
    },
    {
      icon: Globe,
      title: "Science & Social Learning",
      description: "Encouraging curiosity about the world while developing problem-solving and critical thinking.",
      color: "indigo"
    }
  ];

  const getColorClasses = (color) => {
    const colors = {
      blue: "bg-blue-50 border-blue-200 hover:bg-blue-100",
      green: "bg-green-50 border-green-200 hover:bg-green-100",
      orange: "bg-orange-50 border-orange-200 hover:bg-orange-100",
      purple: "bg-purple-50 border-purple-200 hover:bg-purple-100",
      pink: "bg-pink-50 border-pink-200 hover:bg-pink-100",
      indigo: "bg-indigo-50 border-indigo-200 hover:bg-indigo-100"
    };
    return colors[color] || colors.blue;
  };

  const getIconColorClasses = (color) => {
    const colors = {
      blue: "bg-blue-500",
      green: "bg-green-500",
      orange: "bg-orange-500",
      purple: "bg-purple-500",
      pink: "bg-pink-500",
      indigo: "bg-indigo-500"
    };
    return colors[color] || colors.blue;
  };

  return (
    <section id="curriculum" className="py-20 bg-gray-50">
      <div className="max-w-6xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            Our Curriculum
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            A rich curriculum experience that gives each child what they need to reach their unique 
            student potential through play and goal-oriented learning.
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-orange-500 mx-auto rounded-full mt-6"></div>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {curriculumItems.map((item, index) => {
            const Icon = item.icon;
            return (
              <div 
                key={index}
                className={`p-6 rounded-xl border-2 transition-all duration-300 hover:scale-105 hover:shadow-lg ${getColorClasses(item.color)}`}
              >
                <div className={`w-16 h-16 rounded-full ${getIconColorClasses(item.color)} flex items-center justify-center mb-4 mx-auto`}>
                  <Icon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-xl font-bold text-gray-800 text-center mb-3">
                  {item.title}
                </h3>
                <p className="text-gray-600 text-center leading-relaxed">
                  {item.description}
                </p>
              </div>
            );
          })}
        </div>

        <div className="text-center mt-12">
          <p className="text-lg text-gray-700 mb-6">
            Want to learn more about our teaching approach?
          </p>
          <button className="bg-gradient-to-r from-blue-500 to-orange-500 text-white px-8 py-3 rounded-full font-semibold hover:shadow-lg transition-all duration-300 hover:scale-105">
            Download Curriculum Guide
          </button>
        </div>
      </div>
    </section>
  );
};

export default Curriculum;