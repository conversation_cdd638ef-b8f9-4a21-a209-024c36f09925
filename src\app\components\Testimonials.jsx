"use client";
import React, { useState } from 'react';
import { ChevronLeft, ChevronR<PERSON>, Quo<PERSON>, <PERSON> } from 'lucide-react';

const Testimonials = () => {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const testimonials = [
    {
      name: "<PERSON>",
      role: "<PERSON><PERSON> of Emma (Age 5)",
      image: "https://images.unsplash.com/photo-1494790108755-2616c739c2e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80",
      quote: "I just told my child's school... '<PERSON> has flourished beyond my expectations. The individualized attention and inclusive environment have helped her gain confidence and develop strong foundational skills.'",
      rating: 5,
      highlight: "Gained confidence"
    },
    {
      name: "<PERSON>", 
      role: "Parent of <PERSON> (Age 4)",
      image: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80",
      quote: "The teachers here understand that every child learns differently. <PERSON> has made incredible progress in both social skills and academic readiness since joining.",
      rating: 5,
      highlight: "Incredible progress"
    },
    {
      name: "<PERSON>",
      role: "Parent of <PERSON> (Age 6)", 
      image: "https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&q=80",
      quote: "This school has been a blessing for our family. Sofia, who has special needs, receives the support she needs while being part of a loving, inclusive community.",
      rating: 5,
      highlight: "Loving community"
    }
  ];

  const nextTestimonial = () => {
    setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
  };

  const prevTestimonial = () => {
    setCurrentTestimonial((prev) => (prev - 1 + testimonials.length) % testimonials.length);
  };

  const currentData = testimonials[currentTestimonial];

  return (
    <section className="py-20 bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="max-w-6xl mx-auto px-6">
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl font-bold text-gray-800 mb-6">
            Student Success Stories
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Discover the inspiring journeys and achievements of our students at Inclusive Learning School.
          </p>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-orange-500 mx-auto rounded-full mt-6"></div>
        </div>

        <div className="relative bg-white rounded-2xl shadow-xl p-8 md:p-12 max-w-4xl mx-auto">
          {/* Quote Icon */}
          <div className="absolute top-6 left-6 bg-blue-100 rounded-full p-3">
            <Quote className="h-6 w-6 text-blue-600" />
          </div>

          <div className="grid md:grid-cols-3 gap-8 items-center">
            {/* Testimonial Content */}
            <div className="md:col-span-2">
              <div className="flex items-center mb-4">
                {[...Array(currentData.rating)].map((_, i) => (
                  <Star key={i} className="h-5 w-5 fill-yellow-400 text-yellow-400" />
                ))}
              </div>
              
              <blockquote className="text-lg md:text-xl text-gray-700 leading-relaxed mb-6 italic">
                "{currentData.quote}"
              </blockquote>
              
              <div className="flex items-center space-x-4">
                <div>
                  <h4 className="font-bold text-gray-800 text-lg">{currentData.name}</h4>
                  <p className="text-gray-600">{currentData.role}</p>
                  <div className="bg-gradient-to-r from-blue-500 to-orange-500 text-white px-3 py-1 rounded-full text-sm mt-2 inline-block">
                    {currentData.highlight}
                  </div>
                </div>
              </div>
            </div>

            {/* Parent Image */}
            <div className="text-center">
              <img 
                src={currentData.image}
                alt={currentData.name}
                className="w-32 h-32 rounded-full mx-auto shadow-lg object-cover"
              />
            </div>
          </div>

          {/* Navigation */}
          <div className="flex justify-between items-center mt-8">
            <button 
              onClick={prevTestimonial}
              className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors duration-200"
            >
              <ChevronLeft className="h-6 w-6 text-gray-600" />
            </button>

            {/* Dots Indicator */}
            <div className="flex space-x-2">
              {testimonials.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentTestimonial(index)}
                  className={`w-3 h-3 rounded-full transition-all duration-200 ${
                    index === currentTestimonial 
                      ? 'bg-blue-500 scale-125' 
                      : 'bg-gray-300 hover:bg-gray-400'
                  }`}
                />
              ))}
            </div>

            <button 
              onClick={nextTestimonial}
              className="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors duration-200"
            >
              <ChevronRight className="h-6 w-6 text-gray-600" />
            </button>
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center mt-12">
          <p className="text-lg text-gray-700 mb-6">
            Ready to start your child's success story?
          </p>
          <button className="bg-gradient-to-r from-blue-500 to-orange-500 text-white px-8 py-3 rounded-full font-semibold hover:shadow-lg transition-all duration-300 hover:scale-105">
            Schedule a Visit Today
          </button>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;